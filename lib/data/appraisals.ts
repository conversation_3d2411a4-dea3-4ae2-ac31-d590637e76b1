import type { EmployeeAppraisal, AppraisalDetails, UserRole } from '../types'
import { db } from '../db'
import { getEmployees } from './employees'
import { getManagers } from './managers'
import { getPeriods } from './periods'
import { debug } from '../debug'

export async function getManagerAppraisals(): Promise<EmployeeAppraisal[]> {
  try {
    const { getCurrentUser, hasSuperAdminAccess } = await import('../auth')
    const currentUser = await getCurrentUser()

    if (!currentUser) {
      console.error('No authenticated user found')
      return []
    }

    debug.log('🔍 [DEBUG] getManagerAppraisals - Current user:', {
      id: currentUser.id,
      fullName: currentUser.fullName,
      role: currentUser.role
    })

    const employees = await getEmployees()

    debug.log('👥 [DEBUG] getManagerAppraisals - All employees:', employees.map(emp => ({
      id: emp.id,
      fullName: emp.fullName,
      managerId: emp.managerId,
      managerName: emp.managerName
    })))

    // Super-admins see all employees, managers see only their managed employees
    const managedEmployees = hasSuperAdminAccess(currentUser)
      ? employees
      : employees.filter(emp => emp.managerId === currentUser.id)

    debug.log('🎯 [DEBUG] getManagerAppraisals - Managed employees:', managedEmployees.map(emp => ({
      id: emp.id,
      fullName: emp.fullName,
      managerId: emp.managerId,
      managerName: emp.managerName
    })))

    debug.log(`📊 [DEBUG] getManagerAppraisals - Found ${managedEmployees.length} employees managed by ${currentUser.fullName} (ID: ${currentUser.id})`)

    // Get user roles for managers (not employees, since employees don't have user accounts)
    const { supabaseAdmin } = await import('../supabase')

    // Get all managers to determine who is a manager
    const managers = await getManagers()

    // Create a mapping of employee names to manager status
    // Since employees and managers have different ID systems, we match by name
    const managerNames = new Set(managers.map(m => m.fullName))

    // Get roles for managers by their user_ids
    const managerUserIds = managers.map(m => m.id)
    const { data: managerRoles } = await supabaseAdmin
      .from('appy_user_roles')
      .select('user_id, role')
      .in('user_id', managerUserIds)

    debug.log('🔑 [DEBUG] getManagerAppraisals - Manager roles:', managerRoles)

    // Create a map of manager user_id to role
    const managerRoleMap = new Map(managerRoles?.map(mr => [mr.user_id, mr.role]) || [])

    // Create a map of employee name to role (based on whether they're also a manager)
    const roleMap = new Map<string, string>()

    managedEmployees.forEach(emp => {
      if (managerNames.has(emp.fullName)) {
        // This employee is also a manager, find their manager role
        const manager = managers.find(m => m.fullName === emp.fullName)
        const role = manager ? managerRoleMap.get(manager.id) || 'manager' : 'manager'
        debug.log(`🎯 [DEBUG] Employee ${emp.fullName} is also a manager with role: ${role}`)
        roleMap.set(emp.id, role)
      } else {
        // Regular employee
        debug.log(`👤 [DEBUG] Employee ${emp.fullName} is a regular employee`)
        roleMap.set(emp.id, 'employee')
      }
    })

    debug.log('🗺️ [DEBUG] Final roleMap:', Array.from(roleMap.entries()))



    debug.log('🔍 [DEBUG] Manager names:', Array.from(managerNames))
    debug.log('🔍 [DEBUG] Employee names being checked:', managedEmployees.map(emp => emp.fullName))

    // Get the current active period
    const periods = await getPeriods()
    const currentPeriod = periods.find(p => !p.closed)

    if (!currentPeriod) {
      console.warn('No active appraisal period found')
      // Return employees with not-started status if no active period
      return managedEmployees.map((emp) => ({
        employeeId: emp.id,
        fullName: emp.fullName,
        departmentName: emp.departmentName!,
        status: "not-started" as const,
        submittedAt: undefined,
        role: (roleMap.get(emp.id) as UserRole) || 'employee',
        isManager: managerNames.has(emp.fullName),
        managerName: emp.managerName || undefined,
      }))
    }

    // Get appraisals for the current period
    const appraisals = await db.getAppraisalsWithEmployeeData(currentPeriod.id)

    debug.log('📋 [DEBUG] getManagerAppraisals - Fetched appraisals:', appraisals.map(appraisal => ({
      employee_id: appraisal.employee_id,
      status: appraisal.status,
      submitted_at: appraisal.submitted_at
    })))

    // Create a map of employee ID to appraisal data
    const appraisalMap = new Map(appraisals.map(appraisal => [appraisal.employee_id, appraisal]))

    // Build the result with real appraisal statuses
    return managedEmployees.map((emp) => {
      const appraisal = appraisalMap.get(emp.id)

      // Map database status to UI status
      let status: 'not-started' | 'draft' | 'submitted' = 'not-started'
      let submittedAt: string | undefined = undefined

      if (appraisal) {
        debug.log(`📝 [DEBUG] getManagerAppraisals - Employee ${emp.fullName} appraisal status: ${appraisal.status}`)
        if (appraisal.status === 'submitted') {
          status = 'submitted'
          submittedAt = appraisal.submitted_at || undefined
        } else if (appraisal.status === 'pending') {
          status = 'draft'
        }
      } else {
        debug.log(`📝 [DEBUG] getManagerAppraisals - No appraisal found for employee ${emp.fullName}`)
      }

      return {
        employeeId: emp.id,
        fullName: emp.fullName,
        departmentName: emp.departmentName!,
        status,
        submittedAt,
        role: (roleMap.get(emp.id) as UserRole) || 'employee',
        isManager: managerNames.has(emp.fullName),
        managerName: emp.managerName || undefined,
      }
    })
  } catch (error) {
    console.error('Failed to fetch manager appraisals:', error)
    return []
  }
}

export async function getPreviousAppraisal(employeeId: string): Promise<AppraisalDetails | null> {
  try {
    // Get all periods sorted by date
    const periods = await getPeriods()
    const sortedPeriods = periods.sort((a, b) => new Date(b.periodStart).getTime() - new Date(a.periodStart).getTime())
    
    // Find the current period
    const currentPeriod = sortedPeriods.find(p => !p.closed)
    
    if (!currentPeriod) {
      console.warn('No active appraisal period found')
      return null
    }
    
    // Find the previous period (the most recent closed period)
    const previousPeriod = sortedPeriods.find(p => p.closed && new Date(p.periodStart) < new Date(currentPeriod.periodStart))
    
    if (!previousPeriod) {
      // console.log('No previous appraisal period found')
      return null
    }
    
    // Get the appraisal for the previous period
    const previousAppraisal = await db.getAppraisalByEmployeeId(employeeId, previousPeriod.id)
    
    if (!previousAppraisal) {
      // console.log('No previous appraisal found for employee')
      return null
    }
    
    return {
      id: previousAppraisal.id,
      periodId: previousAppraisal.period_id,
      employeeId: previousAppraisal.employee_id,
      managerId: previousAppraisal.manager_id,
      q1: previousAppraisal.question_1 as 'below-expectations' | 'meets-expectations' | 'exceeds-expectations' | null,
      q2: previousAppraisal.question_2 === 'true' ? true : previousAppraisal.question_2 === 'false' ? false : false,
      q3: previousAppraisal.question_3 || '',
      q4: previousAppraisal.question_4 || '',
      q5: previousAppraisal.question_5 || '',
      status: previousAppraisal.status as 'draft' | 'submitted',
      paymentStatus: previousAppraisal.payment_status as 'ready-to-pay' | 'contact-manager' | null,
      // New fields
      keyContributions: previousAppraisal.key_contributions || '',
      extraInitiatives: previousAppraisal.extra_initiatives || '',
      performanceLacking: previousAppraisal.performance_lacking || '',
      disciplineRating: previousAppraisal.discipline_rating || null,
      disciplineComment: previousAppraisal.discipline_comment || '',
      daysOffTaken: previousAppraisal.days_off_taken || null,
      impactRating: previousAppraisal.impact_rating || null,
      impactComment: previousAppraisal.impact_comment || '',
      qualityRating: previousAppraisal.quality_rating || null,
      qualityComment: previousAppraisal.quality_comment || '',
      collaborationRating: previousAppraisal.collaboration_rating || null,
      collaborationComment: previousAppraisal.collaboration_comment || '',
      skillGrowthRating: previousAppraisal.skill_growth_rating || null,
      skillGrowthComment: previousAppraisal.skill_growth_comment || '',
      readinessPromotion: previousAppraisal.readiness_promotion as 'strong-yes' | 'yes-with-reservations' | 'no-not-yet' | null,
      readinessComment: previousAppraisal.readiness_comment || '',
      compensationRecommendation: previousAppraisal.compensation_recommendation || ''
    }
  } catch (error) {
    console.error('Failed to fetch previous appraisal:', error)
    return null
  }
}

export async function getAppraisalDetails(employeeId: string): Promise<AppraisalDetails | null> {
  try {
    // Get the current active period (in real app, this would be more sophisticated)
    const periods = await getPeriods()
    const currentPeriod = periods.find(p => !p.closed)

    if (!currentPeriod) {
      console.warn('No active appraisal period found')
      return null
    }

    const appraisal = await db.getAppraisalByEmployeeId(employeeId, currentPeriod.id)

    if (!appraisal) {
      // No current appraisal exists, try to prefill from previous period
      const previousAppraisal = await getPreviousAppraisal(employeeId)
      
      if (previousAppraisal) {
        // Return a new draft appraisal structure prefilled with previous data
        // console.log('Prefilling appraisal with previous month data')
        return {
          id: '', // Will be generated when saved
          periodId: currentPeriod.id,
          employeeId,
          managerId: '', // Will be set by the current user
          q1: previousAppraisal.q1, // Prefill from previous
          q2: previousAppraisal.q2, // Prefill from previous
          q3: previousAppraisal.q3, // Prefill from previous
          q4: '', // Reset this field as it's month-specific
          q5: '', // Reset this field as it's month-specific
          status: 'draft'
        }
      }
      
      // No previous appraisal found, return empty structure
      return {
        id: '', // Will be generated when saved
        periodId: currentPeriod.id,
        employeeId,
        managerId: '', // Will be set by the current user
        q1: null,
        q2: false,
        q3: '',
        q4: '',
        q5: '',
        status: 'draft'
      }
    }

    return {
      id: appraisal.id,
      periodId: appraisal.period_id,
      employeeId: appraisal.employee_id,
      managerId: appraisal.manager_id,
      q1: appraisal.question_1 as 'below-expectations' | 'meets-expectations' | 'exceeds-expectations' | null,
      q2: appraisal.question_2 === 'true' ? true : appraisal.question_2 === 'false' ? false : false,
      q3: appraisal.question_3 || '',
      q4: appraisal.question_4 || '',
      q5: appraisal.question_5 || '',
      status: appraisal.status as 'draft' | 'submitted',
      paymentStatus: appraisal.payment_status as 'ready-to-pay' | 'contact-manager' | null,
      revisionNumber: appraisal.revision_number || 1,
      isRevision: appraisal.is_revision || false,
      originalSubmissionDate: appraisal.original_submission_date || undefined,
      lastEditedAt: appraisal.last_edited_at || appraisal.created_at,
      // New fields
      keyContributions: appraisal.key_contributions || '',
      extraInitiatives: appraisal.extra_initiatives || '',
      performanceLacking: appraisal.performance_lacking || '',
      disciplineRating: appraisal.discipline_rating || null,
      disciplineComment: appraisal.discipline_comment || '',
      daysOffTaken: appraisal.days_off_taken || null,
      impactRating: appraisal.impact_rating || null,
      impactComment: appraisal.impact_comment || '',
      qualityRating: appraisal.quality_rating || null,
      qualityComment: appraisal.quality_comment || '',
      collaborationRating: appraisal.collaboration_rating || null,
      collaborationComment: appraisal.collaboration_comment || '',
      skillGrowthRating: appraisal.skill_growth_rating || null,
      skillGrowthComment: appraisal.skill_growth_comment || '',
      readinessPromotion: appraisal.readiness_promotion as 'strong-yes' | 'yes-with-reservations' | 'no-not-yet' | null,
      readinessComment: appraisal.readiness_comment || '',
      compensationRecommendation: appraisal.compensation_recommendation || ''
    }
  } catch (error) {
    console.error('Failed to fetch appraisal details:', error)
    return null
  }
}

// Get pending appraisals for approval based on user role
export async function getPendingApprovals(): Promise<EmployeeAppraisal[]> {
  try {
    const { getCurrentUser, canApproveAppraisals } = await import('../auth')
    const currentUser = await getCurrentUser()

    if (!currentUser || !canApproveAppraisals(currentUser.role)) {
      console.error('User not authorized to view pending approvals')
      return []
    }

    console.log('🔍 [DEBUG] getPendingApprovals - Current user:', {
      id: currentUser.id,
      fullName: currentUser.fullName,
      role: currentUser.role
    })

    // Get the current active period
    const periods = await getPeriods()
    const currentPeriod = periods.find(p => !p.closed)

    if (!currentPeriod) {
      console.warn('No active appraisal period found')
      return []
    }

    // Get all submitted appraisals for the current period
    const submittedAppraisals = await db.getSubmittedAppraisals(currentPeriod.id)
    const employees = await getEmployees()
    
    console.log('📋 [DEBUG] getPendingApprovals - Submitted appraisals:', submittedAppraisals.length)

    // Filter appraisals that need approval based on role hierarchy
    const pendingApprovals: EmployeeAppraisal[] = []

    for (const appraisal of submittedAppraisals) {
      const employee = employees.find(emp => emp.id === appraisal.employee_id)
      if (!employee) continue

      // Get the role of the employee who submitted the appraisal
      // For now, assume all submitters are managers - this could be enhanced to look up actual roles
      const submitterRole = 'manager' // Default to manager if not specified

      // Check if current user can approve this appraisal
      const { canApproveAppraisal } = await import('../auth')
      const canApprove = await canApproveAppraisal(submitterRole as any)

      if (canApprove) {
        pendingApprovals.push({
          employeeId: employee.id,
          fullName: employee.fullName,
          departmentName: employee.departmentName!,
          status: 'submitted' as const,
          submittedAt: appraisal.submitted_at || undefined,
        })
      }
    }

    return pendingApprovals
  } catch (error) {
    console.error('Failed to fetch pending approvals:', error)
    return []
  }
}

// Approve an appraisal
export async function approveAppraisal(appraisalId: string, approvedBy: string): Promise<boolean> {
  try {
    const result = await db.updateAppraisalStatus(appraisalId, 'approved', approvedBy)
    return result
  } catch (error) {
    console.error('Failed to approve appraisal:', error)
    return false
  }
}

// Reject an appraisal
export async function rejectAppraisal(appraisalId: string, rejectedBy: string, reason?: string): Promise<boolean> {
  try {
    const result = await db.updateAppraisalStatus(appraisalId, 'rejected', rejectedBy, reason)
    return result
  } catch (error) {
    console.error('Failed to reject appraisal:', error)
    return false
  }
}