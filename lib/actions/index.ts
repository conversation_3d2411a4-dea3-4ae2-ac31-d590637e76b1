// Re-export all actions from their respective files
// This allows for backward compatibility while organizing actions by domain
// Note: Each individual action module contains "use server" directive

// Export shared utilities
export {
  AuthenticationError,
  AuthorizationError,
  ValidationError,
  RateLimitError,
  handleServerActionError
} from './shared'

// Export department actions
export {
  saveDepartmentAction
} from './departments'

// Export manager actions
export {
  saveManagerAction,
  createManagerAndRedirectAction
} from './managers'

// Export employee actions
export {
  saveEmployeeAction,
  deleteEmployeeAction,
  assignManagersToEmployeeAction
} from './employees'

// Export period actions
export {
  savePeriodAction
} from './periods'

// Export appraisal actions
export {
  saveAppraisalDraftAction,
  submitAppraisalAction,
  createAppraisalRevisionAction,
  resubmitAppraisalRevisionAction,
  createRevisionFromSubmittedAction
} from './appraisals'

// Export search actions
export {
  searchAllEntities
} from './search'

// Export approval actions
export {
  approveAppraisalAction,
  rejectAppraisalAction
} from './approvals'

// Export bulk actions
export {
  bulkAppraisalAction
} from './bulk'

// Export export actions
export {
  exportAppraisalsAction
} from './export'

// Export PTO actions
export {
  submitPTORequestAction,
  approvePTORequestAction,
  cancelPTORequestAction,
  initializePTOBalancesAction
} from './pto'